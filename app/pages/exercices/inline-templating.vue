<template>
    <div class="container mx-auto p-4 md:p-8">
        <div class="card bg-base-100 shadow-xl rounded-box">
            <div class="card-body">
                <h5 class="card-title text-2xl font-bold">Interpolation en Ligne : Mode Composition</h5>

                <div class="prose max-w-none mt-4">
                    <p>
                        <!-- Avec String interpolation {{}} le code HTML n'est pas interprété -->
                        <span class="badge badge-info">Interpolation classique :</span>
                        <code>{{ unTemplateLivre }}</code>
                    </p>
                    <p class="mt-4">
                        <!-- Directive v-html pour afficher le code HTML contenu dans la variable -->
                        <span class="badge badge-warning">Directive v-html :</span>
                    </p>
                    <div v-html="unTemplateLivre" class="mt-2 text-center"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='js'>
// import { ref } from 'vue'

const unTemplateLivre = ref('<h1>Les mémoires de <PERSON></h1>')
</script>

<style scoped lang="css">
/* Les styles sont gérés par DaisyUI et Tailwind, donc pas de CSS nécessaire ici. */
</style>
