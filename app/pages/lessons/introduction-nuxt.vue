<template>
    <div class="container mx-auto p-4 md:p-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold text-primary mb-4">
                L'histoire de Nuxt
            </h1>
            <p class="text-lg md:text-xl text-base-content opacity-75 max-w-2xl mx-auto">
                Découvrez l'évolution de ce framework Vue.js révolutionnaire, de ses débuts à son rôle de leader
                aujourd'hui.
            </p>
        </div>

        <!-- Chronologie en cartes -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Nuxt 1.0 (2016) -->
            <div class="card bg-base-100 shadow-xl image-full">
                <figure><img src="https://placehold.co/600x400/3abff8/ffffff?text=Nuxt+1.0" alt="Nuxt 1.0"
                        class="w-full h-full object-cover" /></figure>
                <div class="card-body">
                    <h2 class="card-title text-white">Nuxt 1.0</h2>
                    <p class="text-white opacity-90 text-sm">
                        Né en octobre 2016, Nuxt 1.0 est la première version stable du framework. Il a été créé par la
                        communauté et a rapidement gagné en popularité en offrant un moyen simple de construire des
                        applications Vue.js universelles. Son objectif : rendre le développement côté serveur et le
                        rendu statique accessibles à tous.
                    </p>
                </div>
            </div>

            <!-- Nuxt 2 (2018) -->
            <div class="card bg-base-100 shadow-xl image-full">
                <figure><img src="https://placehold.co/600x400/4ade80/ffffff?text=Nuxt+2" alt="Nuxt 2"
                        class="w-full h-full object-cover" /></figure>
                <div class="card-body">
                    <h2 class="card-title text-white">Nuxt 2</h2>
                    <p class="text-white opacity-90 text-sm">
                        Lancé en mai 2018, Nuxt 2 a été une refonte majeure, apportant de nombreuses améliorations de
                        performance et une meilleure expérience pour les développeurs. Il a introduit un écosystème de
                        modules robustes, permettant aux utilisateurs d'ajouter facilement des fonctionnalités telles
                        que l'authentification et l'intégration CMS, ce qui a consolidé sa position.
                    </p>
                </div>
            </div>

            <!-- Nuxt 3 (2022) -->
            <div class="card bg-base-100 shadow-xl image-full">
                <figure><img src="https://placehold.co/600x400/a855f7/ffffff?text=Nuxt+3" alt="Nuxt 3"
                        class="w-full h-full object-cover" /></figure>
                <div class="card-body">
                    <h2 class="card-title text-white">Nuxt 3</h2>
                    <p class="text-white opacity-90 text-sm">
                        Nuxt 3, sorti en 2022, est la dernière version majeure. Il est bâti sur l'écosystème Vue 3, Vite
                        et Rollup, ce qui le rend plus rapide et plus léger. Cette version intègre une prise en charge
                        complète de TypeScript et un nouveau système de modules plus puissant, marquant une nouvelle ère
                        pour le développement web.
                    </p>
                </div>
            </div>

            <!-- L'avenir de Nuxt -->
            <div class="card bg-base-100 shadow-xl image-full">
                <figure><img src="https://placehold.co/600x400/22d3ee/ffffff?text=L'Avenir" alt="L'Avenir"
                        class="w-full h-full object-cover" /></figure>
                <div class="card-body">
                    <h2 class="card-title text-white">L'avenir de Nuxt</h2>
                    <p class="text-white opacity-90 text-sm">
                        Avec l'innovation constante, l'équipe Nuxt continue de pousser les limites du développement web.
                        Le framework est aujourd'hui plus que jamais axé sur la performance, la simplicité et la
                        flexibilité, permettant aux développeurs de créer des applications web modernes, robustes et
                        hautement optimisées.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// Pas de logique spécifique ici pour le composant d'introduction.
</script>

<style scoped>
/* Les styles sont gérés par DaisyUI et Tailwind, pas de CSS nécessaire. */
</style>