<template>
    <div class="container mx-auto p-4 md:p-8">
        <div class="card bg-base-100 shadow-xl rounded-box p-6">
            <h5 class="card-title text-2xl font-bold">Two-Way Data Binding Mode : Composition</h5>

            <!-- Section V-on & V-bind -->
            <div class="mt-6 mb-4 card bg-base-200 shadow-lg rounded-box p-4">
                <h6 class="text-xl font-semibold mb-2">v-on (keyup) + v-bind (value)</h6>
                <p class="text-sm opacity-75 mb-4">
                    Combinaison de `v-on` (pour écouter les événements clavier) et `v-bind` (pour lier la valeur de
                    l'input), permettant un effet de liaison bidirectionnelle manuelle.
                </p>
                <label class="form-control w-full">
                    <div class="label">
                        <span class="label-text">Nom d'utilisateur (manuel)</span>
                    </div>
                    <input v-on:keyup="afficherNameUser" v-bind:value="nameUser" type="text"
                        placeholder="Écrivez votre nom ici" class="input input-bordered w-full" />
                </label>
                <div class="mt-3 p-3 bg-base-100 rounded-box shadow">
                    <p>La valeur de l'input est : <span class="badge badge-accent">{{ nameUser }}</span></p>
                </div>
            </div>

            <!-- Section V-model -->
            <div class="mt-6 mb-4 card bg-base-200 shadow-lg rounded-box p-4">
                <h6 class="text-xl font-semibold mb-2">v-model</h6>
                <p class="text-sm opacity-75 mb-4">
                    La directive `v-model` simplifie la liaison bidirectionnelle en combinant automatiquement l'écoute
                    d'événements et la mise à jour des données.
                </p>
                <label class="form-control w-full">
                    <div class="label">
                        <span class="label-text">Nom d'utilisateur (automatique avec v-model)</span>
                    </div>
                    <input v-model="userName" type="text" placeholder="Écrivez votre nom ici"
                        class="input input-bordered w-full" />
                </label>
                <div class="mt-3 p-3 bg-base-100 rounded-box shadow">
                    <p>La valeur de l'input est : <span class="badge badge-primary">{{ userName }}</span></p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='js'>
// import { ref } from 'vue'

const nameUser = ref('')
const userName = ref('')

function afficherNameUser(event) {
    nameUser.value = event.target.value;
}
</script>

<style scoped lang="css">
/* Les styles sont gérés par DaisyUI et Tailwind, pas de CSS nécessaire ici. */
</style>