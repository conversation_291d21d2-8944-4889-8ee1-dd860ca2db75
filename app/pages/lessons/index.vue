<template>
    <div class="container mx-auto p-4 md:p-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold text-primary mb-4">
                Toutes les Leçons
            </h1>
            <p class="text-lg md:text-xl text-base-content opacity-75 max-w-2xl mx-auto">
                Découvrez et explorez nos leçons pour approfondir vos connaissances sur Nuxt et le développement web.
            </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Boucle sur un tableau de leçons -->
            <div v-for="lesson in lessons" :key="lesson.id"
                class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300 rounded-box">
                <figure class="px-6 pt-6">
                    <img :src="lesson.image" :alt="lesson.title" class="rounded-xl w-full h-48 object-cover" />
                </figure>
                <div class="card-body items-center text-center">
                    <h2 class="card-title text-2xl font-bold text-primary">{{ lesson.title }}</h2>
                    <p class="text-base-content opacity-80 mt-2">{{ lesson.description }}</p>

                    <!-- Catégorie, framework et tags -->
                    <div class="mt-4 w-full text-left flex flex-wrap gap-2">
                        <div class="badge badge-primary badge-sm">{{ lesson.framework }}</div>
                        <div class="badge badge-outline badge-sm">{{ lesson.category }}</div>
                        <div v-for="tag in lesson.tags" :key="tag" class="badge badge-secondary badge-sm">{{ tag }}
                        </div>
                    </div>

                    <div class="card-actions justify-end mt-4">
                        <NuxtLink :to="`/lessons/${lesson.link}`" class="btn btn-primary btn-sm md:btn-md rounded-box">
                            Commencer
                        </NuxtLink>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

// Données fictives pour les leçons
const lessons = ref([
    {
        id: 1,
        title: 'Les bases de Vue.js',
        description: 'Une introduction complète aux concepts fondamentaux de vue, incluant les composants, les directives les événements, les props, les slots, les directives, les mixins, les plugins, les transitions, les animations, les directives, les mixins, les plugins, les transitions, les animations, les directives, les mixins, les plugins, les transitions, les animations.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Introduction',
        framework: 'vue',
        link: 'introduction-vue'
    },
    {
        id: 100,
        title: 'Syntaxe de Vue.js',
        description: 'Découvrez les deux approches pour écrire vos composants : le Mode Options et le Mode Composition.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Syntaxe',
        framework: 'vue',
        link: 'syntax'
    },

    {
        id: 2,
        title: 'Setup d\'installation et configuration d\'une  application\' Vue.js',
        description: 'Découvrez comment installer Vue.js, configurer votre environnement de développement et créer votre première application Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/bd8aff/ffffff?text=Vue+Setup',
        framework: 'vue',
        link: 'introduction-vue'
    },
    {
        id: 3,
        title: 'Interpolation de Texte avec Vue',
        description: 'Découvrez comment afficher différents types de données dans le template et executer des fonctions (methodes et plus à voir).',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Interpolation',
        framework: 'vue',
        link: 'text-interpolation'
    },
    {
        id: 4,
        title: 'Directive v-html',
        description: 'Découvrez comment afficher du contenu HTML dynamique dans votre application Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+v-html',
        framework: 'vue',
        link: 'inline-templating'
    },
    {
        id: 5,
        title: 'Directive v-bind',
        description: 'Découvrez comment lier dynamiquement des attributs HTML à des données Vue.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+v-bind',
        framework: 'vue',
        link: 'attribute-binding'
    },
    {
        id: 6,
        title: 'Directive v-on',
        description: 'Découvrez comment écouter les événements utilisateur et y répondre de manière réactive.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+v-on',
        framework: 'vue',
        link: 'event-binding'
    },
    {
        id: 7,
        title: 'Directive v-model',
        description: 'Découvrez comment créer une liaison bidirectionnelle entre les données et les entrées utilisateur.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+v-model',
        framework: 'vue',
        link: 'two-way-binding'
    },
    {
        id: 8,
        title: 'Computed Properties',
        description: 'Découvrez comment calculer des valeurs dérivées de façon réactive et optimisée.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Computed',
        framework: 'vue',
        link: 'computed-properties'
    },
    {
        id: 9,
        title: 'Watchers',
        description: 'Découvrez comment observer et réagir aux changements des données réactives.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Watchers',
        framework: 'vue',
        link: 'watchers'
    },
    {
        id: 10,
        title: 'Dynamic Styling',
        description: 'Découvrez comment appliquer des styles et des classes dynamiquement en fonction des données.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Dynamic+Styling',
        framework: 'vue',
        link: 'dynamic-styling'
    },
    {
        id: 11,
        title: 'Conditional Rendering',
        description: 'Découvrez comment afficher ou masquer des éléments en fonction de conditions dynamiques.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Conditional+Rendering',
        framework: 'vue',
        link: 'conditional-rendering'
    },
    {
        id: 12,
        title: 'List Rendering',
        description: 'Découvrez comment afficher des listes d\'éléments de manière dynamique et réactive.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+List+Rendering',
        framework: 'vue',
        link: 'list-rendering'
    },
    {
        id: 13,
        title: 'Props',
        description: 'Découvrez comment transmettre des données d\'un composant parent à un composant enfant.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Props',
        framework: 'vue',
        link: 'props'
    },
    {
        id: 14,
        title: 'Events with $emit',
        description: 'Découvrez comment émettre des événements personnalisés d\'un composant enfant vers son parent.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Events',
        framework: 'vue',
        link: 'emit'
    },
    {
        id: 99,
        title: 'Les bases de Nuxt',
        description: 'Une introduction complète aux concepts fondamentaux de Nuxt, incluant les pages, les layouts et les composants.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue', 'nuxt'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Nuxt+Basics',
        framework: 'nuxt',
        link: 'introduction-nuxt'
    },
    {
        id: 2,
        title: 'Routage Dynamique',
        description: 'Apprenez à créer des routes dynamiques pour gérer des pages avec des données variables, comme des articles de blog.',
        category: 'Routage',
        tags: ['intermédiaire', 'routes', 'développement'],
        image: 'https://placehold.co/600x400/4ade80/ffffff?text=Dynamic+Routes',
        framework: 'nuxt'
    },
    {
        id: 3,
        title: 'Gestion des États',
        description: 'Explorez la gestion des états de votre application avec Pinia, la solution de gestion des états recommandée par Nuxt.',
        category: 'Données',
        tags: ['intermédiaire', 'pinia', 'état'],
        image: 'https://placehold.co/600x400/a855f7/ffffff?text=State+Management',
        framework: 'vue'
    },
    {
        id: 4,
        title: 'Déploiement avec Vercel',
        description: 'Un guide étape par étape pour déployer votre application Nuxt sur Vercel, une plateforme simple et rapide.',
        category: 'Déploiement',
        tags: ['avancé', 'vercel', 'hosting'],
        image: 'https://placehold.co/600x400/22d3ee/ffffff?text=Deployment',
        framework: 'other'
    },
    {
        id: 5,
        title: 'Utilisation des API',
        description: 'Connectez votre application à des API externes pour récupérer et afficher des données dynamiques.',
        category: 'Données',
        tags: ['intermédiaire', 'api', 'requêtes'],
        image: 'https://placehold.co/600x400/ff7d40/ffffff?text=API+Usage',
        framework: 'other'
    },
    {
        id: 6,
        title: 'Optimisation de performance',
        description: 'Découvrez les techniques avancées pour rendre votre application Nuxt plus rapide et plus efficace.',
        category: 'Optimisation',
        tags: ['avancé', 'performance', 'vite'],
        image: 'https://placehold.co/600x400/b84149/ffffff?text=Performance',
        framework: 'nuxt'
    },
    {
        id: 7,
        title: 'Création de plugins',
        description: 'Apprenez à créer et à intégrer des plugins personnalisés pour étendre les fonctionnalités de votre application.',
        category: 'Extensions',
        tags: ['avancé', 'plugins', 'développement'],
        image: 'https://placehold.co/600x400/693892/ffffff?text=Plugins',
        framework: 'nuxt'
    },
    {
        id: 8,
        title: 'Tests unitaires',
        description: 'Introduisez les tests unitaires et les tests d\'intégration pour garantir la robustesse de votre code.',
        category: 'Qualité du code',
        tags: ['avancé', 'tests', 'jest'],
        image: 'https://placehold.co/600x400/35654d/ffffff?text=Unit+Testing',
        framework: 'vue'
    },
    {
        id: 9,
        title: 'Accessibilité (A11y)',
        description: 'Assurez-vous que votre application est accessible à tous les utilisateurs, y compris ceux ayant un handicap.',
        category: 'Design',
        tags: ['intermédiaire', 'a11y', 'ergonomie'],
        image: 'https://placehold.co/600x400/7c4937/ffffff?text=Accessibility',
        framework: 'vue'
    },
    {
        id: 10,
        title: 'Internationalisation',
        description: 'Configurez votre application pour prendre en charge plusieurs langues et formats régionaux.',
        category: 'Déploiement',
        tags: ['avancé', 'i18n', 'locale'],
        image: 'https://placehold.co/600x400/546e7a/ffffff?text=i18n',
        framework: 'nuxt'
    },
]);
</script>

<style scoped>
/* Les styles sont gérés par DaisyUI et Tailwind, donc pas de CSS nécessaire. */
</style>