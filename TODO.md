# 📝 Ma liste de tâches

## Objectifs de la semaine

- [ ] Devenir ULTRA PRO Nuxt donc ULTRA Pro VUE donc ULTRA Pro JS
- [ ] Faire page d'accueil stylée pour la page d'accueil index.vue
- [x] Planifier la session de cours
- [ ] Ajouter une section "Témoignages"

## Objectifs du premier sprint (6h30) Mardi 09/09

- [x] Créer un projet VUE 3
- [x] Créer un projet NUXT 4 (module officiel on va tester : nuxt/icon, nuxt/fonts, nuxt/image, nuxt/scripts, (eslint ? bof (ultra vener)et j'ai deja prettier en extension), nuxt/content (comme astro blog basé sur des fichiers md mais nécésite bdd sqlite3), on verra plus tard)
- [x] installer et utiliser daisyUI (ou autre framework css presféré) dans les 2 projets  <https://daisyui.com/docs/install/>
- [x] Expliquer APP vue
- [x] Expliquer APP Nuxt
- [x] Ajouter un composant AppHeader.vue
- [x] Ajouter un composant AppFooter.vue
- [x] Mettre les projet sur github
- [x] faire un deploiement sur vercel et netlify réflexe CDA (CI/CD part 1)
- [x] Lesson sur Text Interpolation {{ msg }}
- [x] Exercice sur Text Interpolation (afficher des données et executer une fonction ⚠️)
- [x] Exercice sur Text Interpolation (executer une fonction qui return des strings dans le template (sécurité : à la fin dans le template c'est du texte (string / text interpolation)))
- [x] Lesson sur les directives v-bind
- [x] ~~Exercice~~ Démo sur les directives v-bind
- [x] TP Data Binding (Profile Card Dr Mario)

  ## Objectifs du second sprint (6h30) Mercredi 10/09

- [ ] Rapido : installer cypress (réfléxe CDA), avec le script raccourci pour améliorer la DX.
- [ ] Radpido : Mettre en place une Github Action pour lancer les tests Cypress sur chaque push (réfléxe CDA)
- [ ] Quizz Random Wheel Concept CLé de JS (mode nuage de mots qui pourra devenir un TP evaluation)
- [ ] Check si Ok : création projet ? ,SPA, vue vs nuxt,composant, on a un pb au coeur de l'application (ca serait **vers** quel fichier), text interpolation, data binding, directives, v-html, v-bind.
- [ ] Exercice utile commun collab: Faire composant page about.vue ( avec des refs off course, text interpolation, data binding, directives v-bind.)
- [ ] Lesson sur les directives v-on (Event Binding)
- [ ] Exercice sur les directives v-on
- [ ] Lesson sur les directives v-model (Two-Way Binding)
- [ ] Exercice sur les directives v-model
- [ ] Faire une page d'accueil (template routing OK /home ou /)
- [ ] Faire une page de contact (faire template + routing)
- [ ] Faire une page de lesson/index (NUXT)
- [ ] ???? Faire fichiers OK iA  
- [ ] Mettre à jour nuxt.config.ts pour inclure le fichier CSS
- [ ] Installer le module @nuxt/image
- [ ] Ajouter une image à la page d'accueil
- [ ] Ajouter un composant AppFooter.vue
- [ ] Ajouter un composant AppLayout.vue

### Notes supplémentaires

TODO Cool Tools:

- [ ] un systeme de quiz random wheel (afin de désigner intéro question oral, designer coupable, chef de projet, responsable, scribe, etc...)

Pensez à vérifier la documentation de DaisyUI pour les classes de couleurs.
Le système de routage de Nuxt est automatique, il suffit de créer un nouveau fichier dans le dossier pages/.
