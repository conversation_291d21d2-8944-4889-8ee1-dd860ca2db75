<template>
    <div class="container mx-auto p-4 md:p-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold text-primary mb-4">
                Tous les exercices
            </h1>
            <p class="text-lg md:text-xl text-base-content opacity-75 max-w-2xl mx-auto">
                Découvrez et explorez nos exercices pour approfondir vos connaissances sur Nuxt et le développement web.
            </p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Boucle sur un tableau d'exercices -->
            <div v-for="lesson in lessons" :key="lesson.id"
                class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-300 rounded-box">
                <figure class="px-6 pt-6">
                    <img :src="lesson.image" :alt="lesson.title" class="rounded-xl w-full h-48 object-cover" />
                </figure>
                <div class="card-body items-center text-center">
                    <h2 class="card-title text-2xl font-bold text-primary">{{ lesson.title }}</h2>
                    <p class="text-base-content opacity-80 mt-2">{{ lesson.description }}</p>

                    <!-- Catégorie, framework et tags -->
                    <div class="mt-4 w-full text-left flex flex-wrap gap-2">
                        <div class="badge badge-primary badge-sm">{{ lesson.framework }}</div>
                        <div class="badge badge-outline badge-sm">{{ lesson.category }}</div>
                        <div v-for="tag in lesson.tags" :key="tag" class="badge badge-secondary badge-sm">{{ tag }}
                        </div>
                    </div>

                    <div class="card-actions justify-end mt-4">
                        <NuxtLink :to="`/exercices/${lesson.link}`" class="btn btn-primary btn-sm md:btn-md rounded-box">
                            Commencer
                        </NuxtLink>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';

// Données fictives pour les leçons
const lessons = ref([
    {
        id: 1,
        title: 'Exercice Text Interpolation',
        description: 'Exercice pour apprendre l\'interpolation de texte dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+Text+Interpolation',
        framework: 'vue',
        link: 'text-interpolation'
    },
    {
        id: 2,
        title: 'Exercice Directive v-html',
        description: 'Exercice pour apprendre la directive v-html dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+v-html',
        framework: 'vue',
        link: 'inline-templating'
    },
    {
        id: 3,
        title: 'Exercice Directive v-bind',
        description: 'Exercice pour apprendre la directive v-bind dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+v-bind',
        framework: 'vue',
        link: 'attribute-binding'
    },
    {
        id: 4,
        title: 'Exercice Directive v-on',
        description: 'Exercice pour apprendre la directive v-on dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+v-on',
        framework: 'vue',
        link: 'event-binding'
    },
    {
        id: 5,
        title: 'Exercice Directive v-model',
        description: 'Exercice pour apprendre la directive v-model dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+v-model',
        framework: 'vue',
        link: 'two-way-binding'
    },
    {
        id: 6,
        title: 'Exercice Computed Properties',
        description: 'Exercice pour apprendre les propriétés calculées dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+Computed',
        framework: 'vue',
        link: 'computed-bug'
    },
    {
        id: 7,
        title: 'Exercice Watchers',
        description: 'Exercice pour apprendre les watchers dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+Watchers',
        framework: 'vue',
        link: 'watchers'
    },
    {
        id: 8,
        title: 'Exercice Dynamic Styling',
        description: 'Exercice pour apprendre le dynamic styling dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+Dynamic+Styling',
        framework: 'vue',
        link: 'dynamic-styling'
    },
    {
        id: 9,
        title: 'Exercice Conditional Rendering',
        description: 'Exercice pour apprendre le conditional rendering dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+Conditional+Rendering',
        framework: 'vue',
        link: 'conditional-rendering'
    },
    {
        id: 10,
        title: 'Exercice List Rendering',
        description: 'Exercice pour apprendre le list rendering dans Vue.js.',
        category: 'Fondamentaux',
        tags: ['débutant', 'vue'],
        image: 'https://placehold.co/600x400/3abff8/ffffff?text=Vue+Exercice+List+Rendering',
        framework: 'vue',
        link: 'list-rendering'
    }

]);
</script>

<style scoped>
/* Les styles sont gérés par DaisyUI et Tailwind, donc pas de CSS nécessaire. */
</style>