{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/fonts": "0.11.4", "@nuxt/icon": "2.0.0", "@nuxt/image": "1.11.0", "@nuxt/scripts": "0.11.13", "@tailwindcss/vite": "^4.1.13", "@unhead/vue": "^2.0.3", "daisyui": "^5.1.9", "nuxt": "^4.1.1", "tailwindcss": "^4.1.13", "vue": "^3.5.21", "vue-router": "^4.5.1"}}